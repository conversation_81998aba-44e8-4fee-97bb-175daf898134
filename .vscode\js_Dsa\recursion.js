'use strict'

// Print N to idx using recursion
function countNtoidx(n,idx,count=0){
    
    if(n<idx)
        return count;
    // console.log(idx);
    return countNtoidx(n,idx+1,count + 1);
    
    // console.log(idx);
}

console.log(countNtoidx(126,59));

// Sum 
function sum(n,idx){
    if(n===idx)
        return n;
    return idx+sum(n,idx+1);
}

console.log(sum(100,56));

// palindrome
function palindrome(str){
    if(str.length<=1)
        return true;
    if(str[0]!==str[str.length-1])
        return false;
    return palindrome(str.slice(1,-1));
}

console.log(palindrome("racecar"));

// sum of digits
function sumOfDigits(n){
    if(n===0)
        return 0;
    return n%10+sumOfDigits(Math.floor(n/10));
}

console.log(sumOfDigits(12345));

// fibonacci
function fibonacci(n){
    if(n===1||n===2)
        return 1;
    return fibonacci(n-1)+fi<PERSON><PERSON><PERSON>(n-2);
}
console.log(fibonacci(10));

// Power
function power(a,b){
    if(b===0)
        return 1;
    return a*power(a,b-1);
}

console.log(power(2, 3));