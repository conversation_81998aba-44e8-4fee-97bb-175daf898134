'use strict'

// Largest Element
function largestElement(arr){
    let max=arr[0];
    for(let i=1;i<arr.length;i++){
        if(arr[i]>max){
            max=arr[i];
        }
    }
    return max;
}

console.log(largestElement([1, 2, 3, 5, 4]));
console.log(largestElement([5, 3, 20, 10, 2]));

// second largest
function secondLargestElement(arr){
    let max=arr[0];
    let secondMax=arr[0];
    for(let i=1;i<arr.length;i++){
        if(arr[i]>max){
            secondMax=max;
            max=arr[i];
        }
        else if(arr[i]>secondMax){
            secondMax=arr[i];
        }
    }
    return secondMax;
}

console.log(secondLargestElement([1, 2, 3, 5, 4]));
console.log(secondLargestElement([5, 3, 20, 10, 2]));

// check if the array is sorted
function isSorted(arr){
    for(let i=1;i<arr.length;i++){
        if(arr[i]<arr[i-1]){
            return false;
        }
    }
    return true;
}

console.log(isSorted([1, 2, 3, 5, 4]));
console.log(isSorted([1, 2, 3, 4, 5]));

// reverse an array
function reverse(arr){
 for(let i=0;i<Math.floor(arr.length/2);++i){
    let temp=arr[i];
    arr[i]=arr[arr.length-1-i];
    arr[arr.length-1-i]=temp;
   }
    return arr;
}

console.log(reverse([1, 2, 3, 5, 4]));

// moving all zeros to the end
function mote(arr){
    let writeIndex = 0;

    // Move all non-zero elements to the front
    for(let i = 0; i < arr.length; i++){
        if(arr[i] !== 0){
            arr[writeIndex++] = arr[i];
            
        }
    }

    // Fill remaining positions with zeros
    while(writeIndex < arr.length){
        arr[writeIndex++] = 0;
      
    }

    return arr;
}

console.log(mote([8,0,5,0,0,7]));
console.log(mote([8,4,0,0,5,7]));

// left rotate by one
function leftRotateByOne(arr){
    let temp=arr[0];
    for(let i=1;i<arr.length;i++){
        arr[i-1]=arr[i];
    }
    arr[arr.length-1]=temp;
    return arr;
}

console.log(leftRotateByOne([1, 2, 3, 4, 5]));