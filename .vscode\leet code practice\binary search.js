"use strict";

// Guess the number
var pick; // This will be set for testing

var guess = function (num) {
  if (num === pick) return 0;
  if (num < pick) return 1; // My number is higher
  return -1; // My number is lower
};

var guessNumber = function (n) {
  let left = 1; // Numbers range from 1 to n
  let right = n;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const result = guess(mid);

    if (result === 0) {
      return mid; // Found the number!
    } else if (result === 1) {
      left = mid + 1; // My number is higher, search right half
    } else {
      right = mid - 1; // My number is lower, search left half
    }
  }

  return -1;
};

pick = 6;
console.log(guessNumber(10));

pick = 1;
console.log(guessNumber(1));

pick = 1;
console.log(guessNumber(2));

// find the peak element
var findPeakElement = function (nums) {
  let left = 0;
  let right = nums.length - 1;

  while (left <= right) {
    let mid = Math.floor((left + right) / 2);

    // Compare mid with its right neighbor
    if (nums[mid] < nums[mid + 1]) {
      // Peak is on the right side
      left = mid + 1;
    } else {
      // Peak is on the left side
      right = mid - 1;
    }
  }

  // When left > right, we found the peak
  return left;
};

console.log(findPeakElement([1, 2, 3, 1]));
console.log(findPeakElement([1, 2, 1, 3, 5, 6, 4]));

// Find median of two sorted arrays
var findMedianSortedArrays = function (nums1, nums2) {
  let i = 0;
  let j = 0;
  let mergedarr = [];
  // merged two sorted arrays
  while (i < nums1.length && j < nums2.length) {
    if (nums1[i] < nums2[j]) {
      mergedarr.push(nums1[i]);
      i++;
    } else {
      mergedarr.push(nums2[j]);
      j++;
    }
  }
  // if the j loop terminates earlier
  while (i < nums1.length) {
    mergedarr.push(nums1[i]);
    i++;
  }
  // if the i loop terminates ealier
  while (j < nums2.length) {
    mergedarr.push(nums2[j]);
    j++;
  }
  // find the middle element of the merged array
  let m = Math.floor((nums1.length + nums2.length) / 2);
  // check if its length is odd and find the median
  if (mergedarr.length % 2 !== 0) {
    return mergedarr[m];
  }
  // check if its length is even and find the median
  else {
    return (mergedarr[m] + mergedarr[m - 1]) / 2;
  }
};

console.log(findMedianSortedArrays([1, 3], [2]));
console.log(findMedianSortedArrays([1, 2], [3, 4]));

// Search a 2D matrix
var searchMatrix = function(matrix, target) {
    if (!matrix || matrix.length === 0 || matrix[0].length === 0) {
        return false;
    }

    let m = matrix.length;    // number of rows
    let n = matrix[0].length; // number of columns
    let left = 0;
    let right = m * n - 1;    // treat as 1D array with indices 0 to m*n-1

    while (left <= right) {
        let mid = Math.floor((left + right) / 2);

        // Convert 1D index to 2D coordinates
        let row = Math.floor(mid / n);
        let col = mid % n;
        let midValue = matrix[row][col];

        if (midValue === target) {
            return true;
        } else if (midValue < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    return false;
};

// Alternative approach: Two-step binary search
var searchMatrixTwoStep = function(matrix, target) {
    if (!matrix || matrix.length === 0 || matrix[0].length === 0) {
        return false;
    }

    let m = matrix.length;

    // Step 1: Find the correct row using binary search
    let top = 0;
    let bottom = m - 1;
    let targetRow = -1;

    while (top <= bottom) {
        let mid = Math.floor((top + bottom) / 2);

        if (target >= matrix[mid][0] && target <= matrix[mid][matrix[mid].length - 1]) {
            targetRow = mid;
            break;
        } else if (target < matrix[mid][0]) {
            bottom = mid - 1;
        } else {
            top = mid + 1;
        }
    }

    if (targetRow === -1) return false;

    // Step 2: Search in the found row
    let left = 0;
    let right = matrix[targetRow].length - 1;

    while (left <= right) {
        let mid = Math.floor((left + right) / 2);

        if (matrix[targetRow][mid] === target) {
            return true;
        } else if (matrix[targetRow][mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    return false;
};

// Test cases
console.log("Search 2D Matrix Tests:");

let matrix1 = [[1,3,5,7],[10,11,16,20],[23,30,34,60]];
console.log(searchMatrix(matrix1, 3)); 
console.log(searchMatrix(matrix1, 13)); 
console.log(searchMatrix(matrix1, 11));

let matrix2 = [[1,4,7,11,15],[2,5,8,12,19],[3,6,9,16,22],[10,13,14,17,24],[18,21,23,26,30]];
console.log(searchMatrix(matrix2, 5)); 
console.log(searchMatrix(matrix2, 14)); 


console.log(searchMatrixTwoStep(matrix1, 3)); 
console.log(searchMatrixTwoStep(matrix1, 13)); 