"use strict";

// Guess the number
var pick; // This will be set for testing

var guess = function (num) {
  if (num === pick) return 0;
  if (num < pick) return 1; // My number is higher
  return -1; // My number is lower
};

var guessNumber = function (n) {
  let left = 1; // Numbers range from 1 to n
  let right = n;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const result = guess(mid);

    if (result === 0) {
      return mid; // Found the number!
    } else if (result === 1) {
      left = mid + 1; // My number is higher, search right half
    } else {
      right = mid - 1; // My number is lower, search left half
    }
  }

  return -1;
};

pick = 6;
console.log(guessNumber(10));

pick = 1;
console.log(guessNumber(1));

pick = 1;
console.log(guessNumber(2));

// find the peak element
var findPeakElement = function(nums) {
    let max=
};

