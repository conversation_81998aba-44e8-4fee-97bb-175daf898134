"use strict";

// bubble sort
function bubbleSort() {
  let arr = [64, 34, 25, 12, 22, 11, 90];
  let n = arr.length;
  for (let i = 0; i < n - 1; i++) {
    for (let j = 0; j < n - i - 1; j++) {
      if (arr[j] > arr[j + 1]) {
        // swap arr[j+1] and arr[j]
        let temp = arr[j];
        arr[j] = arr[j + 1];
        arr[j + 1] = temp;
      }
    }
  }
  return arr;
}

console.log(bubbleSort());

// optimized bubble sort
function optimizedBubbleSort(arr) {
  let n = arr.length;
  let swapped;
  do {
    swapped = false;
    for (let i = 0; i < n - 1; i++) {
      if (arr[i] > arr[i + 1]) {
        // swap arr[i] and arr[i+1]
        let temp = arr[i];
        arr[i] = arr[i + 1];
        arr[i + 1] = temp;
        swapped = true;
      }
    }
    n--;
  } while (swapped);
  return arr;
}

console.log(optimizedBubbleSort([64, 34, 25, 12, 22, 11, 90]));

// selection sort
function selectionSort(arr) {
  let n = arr.length;
  for (let i = 0; i < n - 1; i++) {
    let minIndex = i;
    for (let j = i + 1; j < n; j++) {
      if (arr[j] < arr[minIndex]) {
        minIndex = j;
      }
    }
    // swap arr[i] and arr[minIndex]
    let temp = arr[i];
    arr[i] = arr[minIndex];
    arr[minIndex] = temp;
  }
  return arr;
}

console.log(selectionSort([64, 34, 25, 12, 22, 11, 90]));

// insertion sort
function insertionSort() {
  let arr = [64, 34, 25, 12, 22, 11, 90];
  let n = arr.length;
  for (let i = 1; i < n; i++) {
    let key = arr[i];
    let j = i - 1;
    while (j >= 0 && arr[j] > key) {
      arr[j + 1] = arr[j];
      j--;
    }
    arr[j + 1] = key;
  }
  return arr;
}

console.log(insertionSort());

// merge sort
function mergeSort(arr) {
  if (arr.length <= 1) {
    return arr;
  }
  let mid = Math.floor(arr.length / 2);
  let left = arr.slice(0, mid);
  let right = arr.slice(mid);
  return merge(mergeSort(left), mergeSort(right));
}

function merge(left, right) {
  let result = [];
  let i = 0;
  let j = 0;
  while (i < left.length && j < right.length) {
    if (left[i] < right[j]) {
      result.push(left[i]);
      i++;
    } else {
      result.push(right[j]);
      j++;
    }
  }
  return result.concat(left.slice(i)).concat(right.slice(j));
}

console.log(mergeSort([64, 34, 25, 12, 22, 11, 90]));

// Intersection of two sorted arrays using binary search
function binarySearch(arr, target) {
  let left = 0;
  let right = arr.length - 1;
  while (left <= right) {
    let mid = Math.floor((left + right) / 2);
    if (arr[mid] === target) {
      return true;
    } else if (arr[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }
  return false;
}

function intersection(arr1, arr2) {
  let result = [];
  for (let i = 0; i < arr1.length; i++) {
    if (i > 0 && arr1[i] === arr1[i - 1]) continue;

    if (binarySearch(arr2, arr1[i])) {
      result.push(arr1[i]);
    }
  }
  return result;
}

console.log(intersection([1, 2, 2, 3, 4, 5], [1, 3, 4, 4, 5, 6, 7]));

// count inversions
function countInversions(arr) {
  let count = 0;
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[i] > arr[j]) {
        count++;
      }
    }
  }
  return count;
}

console.log(countInversions([2, 4, 1, 3, 5]));

// count inversions using merge sort
function mergeSortInversions(arr) {
  if (arr.length <= 1) {
    return arr;
  }
  let mid = Math.floor(arr.length / 2);
  let left = arr.slice(0, mid);
  let right = arr.slice(mid);
  return mergeInversions(mergeSortInversions(left), mergeSortInversions(right));
}

function mergeInversions(left, right) {
  let result = [];
  let i = 0;
  let j = 0;
  let count = 0;
  while (i < left.length && j < right.length) {
    if (left[i] <= right[j]) {
      result.push(left[i]);
      i++;
    } else {
      result.push(right[j]);
      j++;
      count += left.length - i;
    }
  }
  return result.concat(left.slice(i)).concat(right.slice(j));
}

console.log(mergeSortInversions([2, 4, 1, 3, 5]));

function partition(arr, p) {
  const n = arr.length;

  // Step 1: Swap pivot with last element
  [arr[p], arr[n - 1]] = [arr[n - 1], arr[p]];

  const pivot = arr[n - 1]; // Now the pivot is at the end
  const temp = [];

  // Step 2: Add elements ≤ pivot (excluding pivot itself)
  for (let i = 0; i < n; i++) {
    if (arr[i] <= pivot) {
      temp.push(arr[i]);
    }
  }

  // Step 4: Add elements > pivot
  for (let i = 0; i < n - 1; i++) {
    if (arr[i] > pivot) {
      temp.push(arr[i]);
    }
  }

  // Step 5: Copy back to original array
  for (let i = 0; i < n; i++) {
    arr[i] = temp[i];
  }

  return arr;
}

console.log(partition([5, 13, 6, 9, 12, 8, 11], 5));

// Lomuto partition
function LomutoPartititon(arr,l,h){
  let pivot =arr[h]
  let i=l-1
  for(let j=l;j<h;j++){
    if(arr[j]<pivot){
      i++;
      [arr[i],arr[j]]=[arr[j],arr[i]]
    }
  }
  [arr[i+1],arr[h]]=[arr[h],arr[i+1]]
  return i+1;
}

console.log(LomutoPartititon([10,80,30,90,70],0,4));

// Hoares Partition
function HoaresPartition(arr,l,h){
  let pivot =arr[l]
  let i=l-1
  let j=h+1
  while(true){
    do{
      i++;
    }while(arr[i]<pivot)
    do{
      j--;
    }while(arr[j]>pivot)
    if(i>=j){
      return j;
    }
    [arr[i],arr[j]]=[arr[j],arr[i]]
   
  }
}

console.log(HoaresPartition([10,80,30,90,70],0,4));

// quick sort using lomuto partition
function quickSortLomuto(arr,l,h){
  if(l<h){
    let p = LomutoPartititon(arr,l,h)
    quickSortLomuto(arr,l,p-1)
    quickSortLomuto(arr,p+1,h)
  }
  return arr;
}

console.log(quickSortLomuto([10,80,30,90,70],0,4));

// quick sort using hoares partition
function quickSortHoares(arr,l,h){
  if(l<h){
    let p = HoaresPartition(arr,l,h)
    quickSortHoares(arr,l,p)
    quickSortHoares(arr,p+1,h)
  }
  return arr;
}

console.log(quickSortHoares([10,80,30,90,70],0,4));

// Merge with out extra space
function MergeWtihoutExtraspace(){
  let arr1 = [1, 3, 5, 7];
  let arr2 = [0, 2, 6, 8, 9];
  let n1 = arr1.length;
  let n2 = arr2.length;
  let i = 0;
  let j = 0;
  while(i<n1 && j<n2){
    if(arr1[i]<arr2[j]){
      i++;
    }
    else{
      let temp = arr2[j];
      arr2[j] = arr1[i];
      arr1[i] = temp;
      i++;
      j++;
    }
  }
  arr1.sort((a,b)=>a-b);
  arr2.sort((a,b)=>a-b);
  return arr1.concat(arr2);
}


