"use strict";

var isValid = function(s) {
    let bracket='(){}[]';
    let stack=[];
    for(let i=0;i<s.length;i++){
        let char=s[i];
        if(bracket.indexOf(char)%2===0){
            stack.push(char);
        }else{
            if(stack.pop()!==bracket[bracket.indexOf(char)-1]){
                return false;
            }
        }
    }
    return stack.length===0;
};

console.log(isValid("()[]{}")); // true
console.log(isValid("(]")); // false
console.log(isValid("([)]")); // false
console.log(isValid("{[]}")); // true

// Merge two sorted lists
var mergeTwoLists = function(list1, list2) {
    // Create a dummy node to simplify the logic
    let dummy = { val: 0, next: null };
    let current = dummy;

    // Traverse both lists and merge them
    while (list1 !== null && list2 !== null) {
        if (list1.val <= list2.val) {
            current.next = list1;
            list1 = list1.next;
        } else {
            current.next = list2;
            list2 = list2.next;
        }
        current = current.next;
    }

    // Attach remaining nodes from either list
    if (list1 !== null) {
        current.next = list1;
    } else {
        current.next = list2;
    }

    // Return the merged list (skip the dummy node)
    return dummy.next;
};

// Helper function to create a linked list from an array
function createLinkedList(arr) {
    if (arr.length === 0) return null;
    let head = { val: arr[0], next: null };
    let current = head;
    for (let i = 1; i < arr.length; i++) {
        current.next = { val: arr[i], next: null };
        current = current.next;
    }
    return head;
}

// Helper function to print a linked list
function printLinkedList(head) {
    let result = [];
    let current = head;
    while (current !== null) {
        result.push(current.val);
        current = current.next;
    }
    return result;
}

// Test cases
let list1 = createLinkedList([1, 2, 4]);
let list2 = createLinkedList([1, 3, 4]);
let merged = mergeTwoLists(list1, list2);
console.log("Merged list:", printLinkedList(merged)); // [1, 1, 2, 3, 4, 4]

list1 = createLinkedList([]);
list2 = createLinkedList([]);
merged = mergeTwoLists(list1, list2);
console.log("Empty lists:", printLinkedList(merged)); // []

list1 = createLinkedList([]);
list2 = createLinkedList([0]);
merged = mergeTwoLists(list1, list2);
console.log("One empty list:", printLinkedList(merged)); // [0]

// Removes duplicates from sorted array
var removeDuplicates = function(nums) {
    let removed=[]
    let i=0
    let j=1
    while(i<nu)
}