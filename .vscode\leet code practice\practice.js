"use strict";

// var isValid = function (s) {
//   let bracket = "(){}[]";
//   let stack = [];
//   for (let i = 0; i < s.length; i++) {
//     let char = s[i];
//     if (bracket.indexOf(char) % 2 === 0) {
//       stack.push(char);
//     } else {
//       if (stack.pop() !== bracket[bracket.indexOf(char) - 1]) {
//         return false;
//       }
//     }
//   }
//   return stack.length === 0;
// };

// console.log(isValid("()[]{}")); // true
// console.log(isValid("(]")); // false
// console.log(isValid("([)]")); // false
// console.log(isValid("{[]}")); // true

// // Merge two sorted lists
// var mergeTwoLists = function (list1, list2) {
//   // Create a dummy node to simplify the logic
//   let dummy = { val: 0, next: null };
//   let current = dummy;

//   // Traverse both lists and merge them
//   while (list1 !== null && list2 !== null) {
//     if (list1.val <= list2.val) {
//       current.next = list1;
//       list1 = list1.next;
//     } else {
//       current.next = list2;
//       list2 = list2.next;
//     }
//     current = current.next;
//   }

//   // Attach remaining nodes from either list
//   if (list1 !== null) {
//     current.next = list1;
//   } else {
//     current.next = list2;
//   }

//   // Return the merged list (skip the dummy node)
//   return dummy.next;
// };

// // Helper function to create a linked list from an array
// function createLinkedList(arr) {
//   if (arr.length === 0) return null;
//   let head = { val: arr[0], next: null };
//   let current = head;
//   for (let i = 1; i < arr.length; i++) {
//     current.next = { val: arr[i], next: null };
//     current = current.next;
//   }
//   return head;
// }

// // Helper function to print a linked list
// function printLinkedList(head) {
//   let result = [];
//   let current = head;
//   while (current !== null) {
//     result.push(current.val);
//     current = current.next;
//   }
//   return result;
// }

// // Test cases
// let list1 = createLinkedList([1, 2, 4]);
// let list2 = createLinkedList([1, 3, 4]);
// let merged = mergeTwoLists(list1, list2);
// console.log("Merged list:", printLinkedList(merged)); // [1, 1, 2, 3, 4, 4]

// list1 = createLinkedList([]);
// list2 = createLinkedList([]);
// merged = mergeTwoLists(list1, list2);
// console.log("Empty lists:", printLinkedList(merged)); // []

// list1 = createLinkedList([]);
// list2 = createLinkedList([0]);
// merged = mergeTwoLists(list1, list2);
// console.log("One empty list:", printLinkedList(merged)); // [0]

// // Removes duplicates from sorted array
// var removeDuplicates = function (nums) {
//   if (nums.length === 0) return 0;

//   let i = 0; // Pointer for unique elements

//   for (let j = 1; j < nums.length; j++) {
//     // If current element is different from previous unique element
//     if (nums[j] !== nums[i]) {
//       i++; // Move to next position for unique element
//       nums[i] = nums[j]; // Place the unique element
//     }
//   }

//   // Return the length of array with unique elements
//   return i + 1;
// };

// // Test cases
// console.log("Test 1:");
// let nums1 = [1, 1, 2];
// let length1 = removeDuplicates(nums1);
// console.log("Length:", length1); // Expected: 2
// console.log("Array:", nums1.slice(0, length1)); // Expected: [1, 2]

// console.log("\nTest 2:");
// let nums2 = [0, 0, 1, 1, 1, 2, 2, 3, 3, 4];
// let length2 = removeDuplicates(nums2);
// console.log("Length:", length2); // Expected: 5
// console.log("Array:", nums2.slice(0, length2)); // Expected: [0, 1, 2, 3, 4]

// console.log("\nTest 3:");
// let nums3 = [1, 2, 3, 4, 5];
// let length3 = removeDuplicates(nums3);
// console.log("Length:", length3); // Expected: 5
// console.log("Array:", nums3.slice(0, length3)); // Expected: [1, 2, 3, 4, 5]

// remove element
var removeElement = function (nums, val) {
  let k = 0; // Pointer for elements not equal to val

  for (let i = 0; i < nums.length; i++) {
    if (nums[i] !== val) {
      nums[k] = nums[i];
      k++;
    }
  }

  return k; // Return the new length
};

// Test cases
console.log("Test 1:");
let nums1 = [3, 2, 2, 3];
let length1 = removeElement(nums1, 3);
console.log("Length:", length1); // Expected: 2
console.log("Array:", nums1.slice(0, length1)); // Expected: [2, 2]

console.log("\nTest 2:");
let nums2 = [0, 1, 2, 2, 3, 0, 4, 2];
let length2 = removeElement(nums2, 2);
console.log("Length:", length2); // Expected: 5
console.log("Array:", nums2.slice(0, length2)); // Expected: [0, 1, 3, 0, 4]

console.log("\nTest 3:");
let nums3 = [1];
let length3 = removeElement(nums3, 1);
console.log("Length:", length3); // Expected: 0
console.log("Array:", nums3.slice(0, length3)); // Expected: []

// find the index of the first occurence
var strStr = function(haystack, needle) {
    let firstOccurence = haystack.indexOf(needle);
    return firstOccurence;
};

console.log(strStr("hello", "ll")); 
console.log(strStr("aaaaa", "bba")); 
console.log(strStr("sadbutsad", "sad")); 

// climbing stairs
// Climbing Stairs - Dynamic Programming approach
var climbStairs = function(n) {
    if (n <= 2) return n;

    let prev2 = 1; // Ways to reach step 1
    let prev1 = 2; // Ways to reach step 2

    for (let i = 3; i <= n; i++) {
        let current = prev1 + prev2; // Ways to reach step i
        prev2 = prev1;
        prev1 = current;
    }

    return prev1;
};

// Alternative recursive approach with memoization (for understanding)
var climbStairsRecursive = function(n, memo = {}) {
    if (n <= 2) return n;
    if (memo[n]) return memo[n];

    memo[n] = climbStairsRecursive(n - 1, memo) + climbStairsRecursive(n - 2, memo);
    return memo[n];
};

// Test cases
console.log("Climbing Stairs Tests:");
console.log("n=1:", climbStairs(1)); 
console.log("n=2:", climbStairs(2)); 
console.log("n=3:", climbStairs(3)); 
console.log("n=4:", climbStairs(4)); 
console.log("n=5:", climbStairs(5)); 

console.log("\nRecursive approach:");
console.log("n=5:", climbStairsRecursive(5)); 

// Same tree
class TreeNode {
    constructor(val, left, right) {
        this.val = (val === undefined ? 0 : val)
        this.left = (left === undefined ? null : left)
        this.right = (right === undefined ? null : right)
    }
}

var isSameTree = function(p, q) {
    if (p === null && q === null) return true;
    if (p === null || q === null) return false;
    if (p.val !== q.val) return false;
    return isSameTree(p.left, q.left) && isSameTree(p.right, q.right);
};

// Helper function to create tree from array representation
function createTreeFromArray(arr) {
    if (!arr || arr.length === 0 || arr[0] === null) return null;

    let root = new TreeNode(arr[0]);
    let queue = [root];
    let i = 1;

    while (queue.length > 0 && i < arr.length) {
        let node = queue.shift();

        // Left child
        if (i < arr.length && arr[i] !== null) {
            node.left = new TreeNode(arr[i]);
            queue.push(node.left);
        }
        i++;

        // Right child
        if (i < arr.length && arr[i] !== null) {
            node.right = new TreeNode(arr[i]);
            queue.push(node.right);
        }
        i++;
    }

    return root;
}

// Test cases
console.log("Same Tree Tests:");

// Test 1: Same trees [1,2,3]
let tree1 = createTreeFromArray([1, 2, 3]);
let tree2 = createTreeFromArray([1, 2, 3]);
console.log("Test 1 - [1,2,3] vs [1,2,3]:", isSameTree(tree1, tree2));

// Test 2: Different structure [1,2] vs [1,null,2]
let tree3 = createTreeFromArray([1, 2]);
let tree4 = createTreeFromArray([1, null, 2]);
console.log("Test 2 - [1,2] vs [1,null,2]:", isSameTree(tree3, tree4)); 

// Test 3: Different values [1,2,1] vs [1,1,2]
let tree5 = createTreeFromArray([1, 2, 1]);
let tree6 = createTreeFromArray([1, 1, 2]);
console.log("Test 3 - [1,2,1] vs [1,1,2]:", isSameTree(tree5, tree6)); 

// Test 4: Both null trees
console.log("Test 4 - null vs null:", isSameTree(null, null)); 

// Test 5: One null, one not null
let tree7 = createTreeFromArray([1]);
console.log("Test 5 - [1] vs null:", isSameTree(tree7, null));

// symmetric tree
var isSymmetric = function(root) {
    if (root === null) return true;
    return isMirror(root.left, root.right);
};

var isMirror = function(left, right) {
    if (left === null && right === null) return true;
    if (left === null || right === null) return false;
    if (left.val !== right.val) return false;
    return isMirror(left.left, right.right) && isMirror(left.right, right.left);
};

let tree=createTreeFromArray([1,2,2,3,4,4,3]);
console.log(isSymmetric(tree));

let tree8=createTreeFromArray([1,2,2,null,3,null,3]);
console.log(isSymmetric(tree8));

// Maximum depth of binary tree
var maxDepth = function(root) {
    // Base case: if node is null, depth is 0
    if (root === null) return 0;

    // Recursively find depth of left and right subtrees
    let leftDepth = maxDepth(root.left);
    let rightDepth = maxDepth(root.right);

    // Return 1 + maximum of left and right depths
    return 1 + Math.max(leftDepth, rightDepth);
};

let tree9 = createTreeFromArray([3, 9, 20, null, null, 15, 7]);
console.log(maxDepth(tree9)); 

let tree10 = createTreeFromArray([1, null, 2]);
console.log(maxDepth(tree10)); 