"use strict";

var isValid = function(s) {
    let bracket='(){}[]';
    let stack=[];
    for(let i=0;i<s.length;i++){
        let char=s[i];
        if(bracket.indexOf(char)%2===0){
            stack.push(char);
        }else{
            if(stack.pop()!==bracket[bracket.indexOf(char)-1]){
                return false;
            }
        }
    }
    return stack.length===0;
};

console.log(isValid("()[]{}")); // true
console.log(isValid("(]")); // false
console.log(isValid("([)]")); // false
console.log(isValid("{[]}")); // true

// Merge two sorted lists
class Node {
  constructor(data, next = null) {
    this.data = data;
    this.next = next;
  }
}

function printList(head) {
  let temp = head;
  while (temp != null) {
    console.log(temp.data);
    temp = temp.next;
  }
}

let head = new Node(1);
head.next = new Node(3);
head.next.next = new Node(5);
head.next.next.next = new Node(8);
head.next.next.next.next = new Node(11);

let head2 = new Node(1);
head2.next = new Node(2);
head2.next.next = new Node(6);
head2.next.next.next = new Node(7);
head2.next.next.next.next = new Node(12);

// Merge two sorted lists
var mergeTwoLists = function(list1, list2) {
    
};