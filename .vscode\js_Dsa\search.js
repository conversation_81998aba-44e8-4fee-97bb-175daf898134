"use strict";

// linear search
function linearSearch(arr, target) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === target) {
      return i;
    }
  }
  return -1;
}

// Example usage:
const numbers = [1, 2, 3, 3, 4, 5];
const targetNumber = 3;
const result = linearSearch(numbers, targetNumber);

console.log(result); // Output: 2 (index of 3 in the array)
console.log(linearSearch([1, 2, 3, 4, 5], 6)); // Output: -1 (target not found)

// binary search
function binarySearch(arr, target) {
  let left = 0;
  let right = arr.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    if (arr[mid] === target) {
      return mid;
    } else if (arr[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  return -1;
}

// Example usage:
const numbers1 = [1, 2, 3, 4, 5];
const targetNumber1 = 4;
const result1 = binarySearch(numbers1, targetNumber1);

console.log(result1); // Output: 2 (index of 3 in the array)
console.log(binarySearch([1, 2, 3, 4, 5], 6)); // Output: -1 (target not found)

// index of first occurrence
function firstOccurrence(arr, target) {
  let left = 0;
  let right = arr.length - 1;
  let result = -1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    if (arr[mid] === target) {
      result = mid;
      right = mid - 1;
    } else if (arr[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  return result;
}

// Example usage:
const numbers2 = [1, 2, 3, 3, 4, 5];
const targetNumber2 = 3;
const result2 = firstOccurrence(numbers2, targetNumber2);

console.log(result2);

// last occurrence
function lastOccurrence(arr, target) {
  let left = 0;
  let right = arr.length - 1;
  let result = -1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    if (arr[mid] === target) {
      result = mid;
      left = mid + 1;
    } else if (arr[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  return result;
}

// Example usage:
const numbers3 = [1, 2, 3, 3, 4, 5];
const targetNumber3 = 3;
const result3 = lastOccurrence(numbers3, targetNumber3);

console.log(result3);

// count occurrence
function countOccurrence(arr, target) {
  const first = firstOccurrence(arr, target);
  const last = lastOccurrence(arr, target);

  if (first === -1 || last === -1) {
    return 0;
  }

  return last - first + 1;
}

// Example usage:
const numbers4 = [1, 2, 3, 3, 3, 3, 4, 5];
const targetNumber4 = 3;
const result4 = countOccurrence(numbers4, targetNumber4);

console.log(result4);
console.log(countOccurrence([0,0,0,1,1,1,1],1));

// smallest postive missing number
function spmn(arr,n){
  let i=1;
  while(i<=n){
    if(arr.includes(i)){
      i++;
    }
    else{
      return i;
    }
  }
  return i;
}

console.log(spmn([0,1,2,3,4,5,7],7));

// effective approach for finding smallest positive missing number
function missingNumber(arr) {

    let n = arr.length;
    for (let i = 0; i < n; i++) {

        // if arr[i] is within the range 1 to n and arr[i] is
        // not placed at (arr[i]-1)th index in arr
        while (arr[i] >= 1 && arr[i] <= n 
               && arr[i] !== arr[arr[i] - 1]) {

            // then swap arr[i] and arr[arr[i]-1] to place 
            // arr[i] to its corresponding index
            let temp = arr[i];
            arr[i] = arr[arr[i] - 1];
            arr[temp - 1] = temp;
        }
    }

    // If any number is not at its corresponding index 
    // it is then missing,
    for (let i = 1; i <= n; i++) {
        if (i !== arr[i - 1]) {
            return i;
        }
    }

    // If all number from 1 to n are present 
    // then n+1 is smallest missing number
    return n + 1;
}

let arr = [2, -3, 4, 1, 1, 7];
console.log(missingNumber(arr));

// majority element using Boyer-Moore Majority Vote Algorithm
function majority(arr){
    let candidate = null;
    let count = 0;

    // Phase 1: Find potential majority candidate
    for(let i = 0; i < arr.length; i++){
        if(count === 0){
            candidate = arr[i];
            count = 1;
        }
        else if(arr[i] === candidate){
            count++;
        }
        else{
            count--;
        }
    }

    // Phase 2: Verify if candidate is actually majority
    count = 0;
    for(let i = 0; i < arr.length; i++){
        if(arr[i] === candidate){
            count++;
        }
    }

    // Return candidate if it appears more than n/2 times
    if(count > arr.length / 2){
        return candidate;
    }

    return null; // No majority element found
}

console.log(majority([1, 1, 2, 2, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5])); // null (no majority)
console.log(majority([4, 4, 4, 4, 4, 1, 2, 3])); // 4 (appears 5 times out of 8)
console.log(majority([1, 2, 3, 2, 2, 2, 5, 4, 2])); // 2 (appears 5 times out of 9)