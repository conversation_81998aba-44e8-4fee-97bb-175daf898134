"use strict";

// Anagrams
var groupAnagrams = function (strs) {
  if (strs.length === 0) return [];
  let map = new Map();
  for (let str of strs) {
    let sorted = str.split("").sort().join("");
    if (!map.has(sorted)) {
      map.set(sorted, []);
    }
    map.get(sorted).push(str);
  }
  return Array.from(map.values());
};

console.log(groupAnagrams(["eat", "tea", "tan", "ate", "nat", "bat"]));

// Merge intervals
var merge = function (intervals) {
  const start = 0;
  const end = 1;

  intervals.sort((a, b) => a[start] - b[start]);
  let previous = intervals[0];
  let result = [previous];
  for (let current of intervals) {
    if (current[start] <= previous[end]) {
      previous[end] = Math.max(current[end], previous[end]);
    } else {
      result.push(current);
      previous = current;
    }
  }
  return result;
};

console.log(
  merge([
    [1, 3],
    [2, 6],
    [8, 10],
    [15, 18],
  ])
);
console.log(
  merge([
    [1, 4],
    [4, 5],
  ])
);
console.log(
  merge([
    [4, 7],
    [1, 4],
  ])
);
// sort colors
var sortColors = function (nums) {
  let n = nums.length;
  let swapped;
  do {
    swapped = false;
    for (let i = 0; i < n - 1; i++) {
      if (nums[i] > nums[i + 1]) {
        // swap nums[i] and nums[i+1]
        let temp = nums[i];
        nums[i] = nums[i + 1];
        nums[i + 1] = temp;
        swapped = true;
      }
    }
    n--;
  } while (swapped);
  return nums;
};

console.log(sortColors([2, 0, 2, 1, 1, 0]));
console.log(sortColors([2, 0, 1]));

// Node class for linked list
class Node {
  constructor(val, next = null) {
    this.val = val;
    this.next = next;
  }
}

// Helper function to create linked list from array
function createLinkedList(arr) {
  if (arr.length === 0) return null;
  let head = new Node(arr[0]);
  let current = head;
  for (let i = 1; i < arr.length; i++) {
    current.next = new Node(arr[i]);
    current = current.next;
  }
  return head;
}

// Helper function to convert linked list to array for display
function linkedListToArray(head) {
  let result = [];
  let current = head;
  while (current) {
    result.push(current.val);
    current = current.next;
  }
  return result;
}

// insertion sort list
var insertionSortList = function (head) {
  if (!head || !head.next) {
    return head; // base case: empty or single-node list
  }

  let dummy = new Node(0); // dummy node to simplify insertions
  let curr = head;

  while (curr) {
    let prev = dummy;

    // find the correct position to insert curr
    while (prev.next && prev.next.val < curr.val) {
      prev = prev.next;
    }

    // store next node before changing links
    let next = curr.next;

    // insert curr between prev and prev.next
    curr.next = prev.next;
    prev.next = curr;

    // move to the next node in the original list
    curr = next;
  }

  return dummy.next;
};

let list1 = createLinkedList([4, 2, 1, 3]);
let sorted1 = insertionSortList(list1);
console.log(linkedListToArray(sorted1));

let list2 = createLinkedList([-1, 5, 3, 4, 0]);
let sorted2 = insertionSortList(list2);
console.log(linkedListToArray(sorted2));

// sort list
// change the sorted array to list
var sortList = function (head) {
  let nodes = sortNodes(head);
  return createLinkedList(nodes);
};

// change the linked list to array, then sort the array in ascending order
var sortNodes = (head) => {
  let arr = [];
  while (head) {
    arr.push(head.val);
    head = head.next;
  }
  return arr.sort((a, b) => a - b);
};

let list3 = createLinkedList([4, 2, 1, 3]);
console.log(sortList(list3));
let list4 = createLinkedList([-1, 5, 3, 4, 0]);
console.log(sortList(list4));

// maximum gap
var maximumGap = function (nums) {
  if (nums.length < 2) return 0;

  // Sort the array
  let sortedNum = nums.sort((a, b) => a - b);
  let maxGap = 0;

  // Find maximum gap between consecutive elements
  for (let i = 1; i < sortedNum.length; i++) {
    let gap = sortedNum[i] - sortedNum[i - 1];
    maxGap = Math.max(maxGap, gap);
  }

  return maxGap;
};

console.log(maximumGap([3, 6, 9, 1]));
console.log(maximumGap([10]));

// largest number
var largestNumber = function(nums) {
    numsString=nums.map(num=>num.toString());
    numsString.sort((a,b)=>(b+a)-(a+b));
    return numsString.join('');
};

console.log(largestNumber([10,2]));
console.log(largestNumber([3,30,34,5,9]));