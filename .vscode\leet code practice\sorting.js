"use strict";

// Anagrams
var groupAnagrams = function(strs) {
    if(strs.length === 0) return[]
    let map = new Map();
    for(let str of strs){
        let sorted = str.split("").sort().join("");
        if(!map.has(sorted)){
            map.set(sorted,[]);
        }
        map.get(sorted).push(str);
    }
    return Array.from(map.values());
};

console.log(groupAnagrams(["eat", "tea", "tan", "ate", "nat", "bat"]));

// Merge intervals
var merge = function(intervals) {
    const start=0
    const end=1

    intervals.sort((a,b)=>a[start]-b[start])
    let previous=intervals[0]
    let result=[previous]
    for(let current of intervals){
        if(current[start]<=previous[end]){
            previous[end]=Math.max(current[end],previous[end])
        }
        else{
            result.push(current)
            previous=current
        }
    }
    return result;
 
};

console.log(merge([[1,3],[2,6],[8,10],[15,18]]));
console.log(merge([[1,4],[4,5]]));
console.log(merge([[4,7],[1,4]]));
// sort colors
var sortColors = function(nums) {
      let n = nums.length;
  let swapped;
  do {
    swapped = false;
    for (let i = 0; i < n - 1; i++) {
      if (nums[i] > nums[i + 1]) {
        // swap nums[i] and nums[i+1]
        let temp = nums[i];
        nums[i] = nums[i + 1];
        nums[i + 1] = temp;
        swapped = true;
      }
    }
    n--;
  } while (swapped);
  return nums;
};

console.log(sortColors([2,0,2,1,1,0]));
console.log(sortColors([2,0,1]));

// insertion sort list
var insertionSortList = function(head) {
    if (!head || !head.next) {
        return head; // base case: empty or single-node list
    }

    let dummy = new Node(0);  // dummy node to simplify insertions
    let curr = head;

    while (curr) {
        let prev = dummy;

        // find the correct position to insert curr
        while (prev.next && prev.next.val < curr.val) {
            prev = prev.next;
        }

        // store next node before changing links
        let next = curr.next;

        // insert curr between prev and prev.next
        curr.next = prev.next;
        prev.next = curr;

        // move to the next node in the original list
        curr = next;
    }

    return dummy.next;
};
