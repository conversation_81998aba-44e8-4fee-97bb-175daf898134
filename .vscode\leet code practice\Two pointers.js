"use strict";

// container with most water
var maxArea = function (height) {
  let l = 0;
  let r = height.length - 1;
  let MaxA = 0;
  if (height.length < 2) return null;
  while (l < r) {
    let minWidth = Math.min(height[l], height[r]);
    let A = minWidth * (r - l);
    MaxA = Math.max(MaxA, A);
    if (height[l] <= height[r]) {
      l++;
    } else if (height[l] > height[r]) {
      r--;
    }
  }
  return MaxA;
};

console.log(maxArea([1, 8, 6, 2, 5, 4, 8, 3, 7]));

// 4 sum
var fourSum = function (nums, target) {
  nums.sort((a, b) => a - b);
  let quadruplets = [];
  for (let i = 0; i < nums.length - 3; i++) {
    if (i > 0 && nums[i] === nums[i - 1]) continue;
    for (let j = i + 1; j < nums.length - 2; j++) {
      if (j > i + 1 && nums[j] === nums[j - 1]) continue;
      let k = j + 1;
      let l = nums.length - 1;
      while (k < l) {
        const sum = nums[i] + nums[j] + nums[k] + nums[l];
        if (sum === target) {
          quadruplets.push([nums[i], nums[j], nums[k], nums[l]]);
          while (k < l && nums[k] === nums[k + 1]) k++;
          while (k < l && nums[l] === nums[l - 1]) l--;
          k++;
          l--;
        } else if (sum < target) {
          k++;
        } else {
          l--;
        }
      }
    }
  }
  return quadruplets;
};

console.log(fourSum([1, 0, -1, 0, -2, 2], 0));

// Next permutation
var nextPermutation = function(nums) {
    let sortedNum=nums((a,b)=>a-b)
    let reversedNum=sortedNum.reverse()
    if
};
