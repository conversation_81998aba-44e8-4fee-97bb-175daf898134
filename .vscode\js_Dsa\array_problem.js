"use strict";

// inserting elements at any position
function insertAtIndex(arr, index, val) {
  arr.splice(index, 0, val);
  return arr;
}

console.log(insertAtIndex([1, 4, 5, 2, 3], 2, 7));

// reverse in groups
function reverseInGroups(arr, k) {
  for (let i = 0; i < arr.length; i += k) {
    let left = i;
    let right = Math.min(i + k - 1, arr.length - 1);
    while (left < right) {
      let temp = arr[left];
      arr[left] = arr[right];
      arr[right] = temp;
      left++;
      right--;
    }
  }
  return arr;
}

console.log(reverseInGroups([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 3));

// Marojiyt wins
function majorityWins(arr, n, x, y) {
  let count_x = 0;
  let count_y = 0;
  for (let i = 0; i < n; i++) {
    if (arr[i] === x) count_x++;
    else if (arr[i] === y) count_y++;

    if (count_x > count_y) return x;
    else if (count_x < count_y) return y;
    else {
      return x < y ? x : y;
    }
  }
}

console.log(majorityWins([1, 1, 2, 2, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5], 14, 4, 5));

// Maximum index
function maximumIndex(arr){
    let n=arr.length
    let maxIndex = 0;
    let maxElement = arr[0];
    for (let i = 1; i < n; i++) {
      if (arr[i] > maxElement) {
        maxElement = arr[i];
        maxIndex = i;
      }
    }
    return maxIndex;

}

console.log(maximumIndex([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]));

// rotate array d times
function rotateArray(arr,d){
let n=arr.length

for(let i=0;i<d;i++){
    let temp=arr[0];
    for(let j=0;j<n-1;j++){
        arr[j]=arr[j+1];
    }
    arr[n-1]=temp;
}
return arr;
}

console.log(rotateArray([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3));