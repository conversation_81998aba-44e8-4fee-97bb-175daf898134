' use strict'

// Hashing using chaining through linked list
class myHash{
constructor(b){
    this.bucket=b
    this.table=[]
    for(let i=0;i<b;i++){
        this.table[i]=[]
    }
}
insert(x){
        let i=x%this.bucket
        this.table[i].push(x)
    }
search(x){
    let i=x%this.bucket
    for(let j=0;j<this.table[i].length;j++){
        if(this.table[i][j]===x){
            return true
        }
    }
    return false
}
remove(x){
    let i=x%this.bucket
    for(let j=0;j<this.table[i].length;j++){
        if(this.table[i][j]===x){
            this.table[i].splice(j,1)
            return
        }
    }
}
}

h=new myHash(7)
h.insert(7)
h.insert(21)
h.insert(28)
h.insert(35)
h.insert(42)
h.insert(49)
h.insert(56)
h.insert(63)
h.insert(70)
h.insert(77)
h.insert(84)
h.insert(91)
h.insert(98)
h.insert(105)
console.log(h.search(105));
console.log(h.search(106));
h.remove(105)
console.log(h.search(105));

// Open addressing
function hash(key, size) {
    return key % size;
  }
  
  function linearProbing(key, i, size) {
    return (hash(key, size) + i) % size;
  }
  
  function quadraticProbing(key, i, size) {
    return (hash(key, size) + i + i * i) % size;
  }
  
  
  // Hash table using open addressing(linear probing)
  class HashTable {
    constructor(size) {
      this.size = size;
      this.table = new Array(size);
    }
    
    insert(key, value) {
    let i = 0;
    let index;
    do {
      index = linearProbing(key, i, this.size);
      if (this.table[index] === undefined || this.table[index] === -1) {
        this.table[index] = { key, value };
        return;
      }
      i++;
    } while (i < this.size);
    throw new Error("Hash table is full");
  }
  
  search(key) {
    let i = 0;
    let index;
    do {
      index = linearProbing(key, i, this.size);
      const entry = this.table[index];
      if (entry === undefined) return false;
      if (entry !== -1 && entry.key === key) {
        return entry.value;
      }
      i++;
    } while (i < this.size);
    return false;
  }

  remove(key) {
    let i = 0;
    let index;
    do {
      index = linearProbing(key, i, this.size);
      const entry = this.table[index];
      if (entry === undefined) return;
      if (entry !== -1 && entry.key === key) {
        this.table[index] = -1;
        return;
      }
      i++;
    } while (i < this.size);
  }
}

const hashTable = new HashTable(11);
hashTable.insert(5, "Value 5");
hashTable.insert(15, "Value 15");
hashTable.insert(25, "Value 25");

console.log(hashTable.search(15)); // Output: "Value 15"

hashTable.remove(15);

console.log(hashTable.search(15)); // Output: false

// Hash table using open addressing(double hashing)
class HashTable2 {
  constructor(size) {
    this.size = size;
    this.table = new Array(size);
    this.deleted = Symbol("deleted");
  }

  // Primary hash function
  hash1(key) {
    return key % this.size;
  }

  // Secondary hash function (must never return 0)
  hash2(key) {
    return 1 + (key % (this.size - 1));
  }

  // Double hashing formula
  doubleHashing(key, i) {
    return (this.hash1(key) + i * this.hash2(key)) % this.size;
  }

  insert(key, value) {
    for (let i = 0; i < this.size; i++) {
      const index = this.doubleHashing(key, i);
      if (this.table[index] === undefined || this.table[index] === this.deleted) {
        this.table[index] = { key, value };
        return;
      }
      // Update if key already exists
      if (this.table[index].key === key) {
        this.table[index].value = value;
        return;
      }
    }
    throw new Error("Hash table is full");
  }

  search(key) {
    for (let i = 0; i < this.size; i++) {
      const index = this.doubleHashing(key, i);
      const entry = this.table[index];

      if (entry === undefined) {
        return false;
      }

      if (entry !== this.deleted && entry.key === key) {
        return entry.value;
      }
    }
    return false;
  }

  remove(key) {
    for (let i = 0; i < this.size; i++) {
      const index = this.doubleHashing(key, i);
      const entry = this.table[index];

      if (entry === undefined) {
        return;
      }

      if (entry !== this.deleted && entry.key === key) {
        this.table[index] = this.deleted;
        return;
      }
    }
  }
}

const hashTable2 = new HashTable2(11);
hashTable2.insert(5, "Value 5");
hashTable2.insert(15, "Value 15");
hashTable2.insert(25, "Value 25");

console.log(hashTable2.search(15)); // Output: "Value 15"

hashTable2.remove(15);

console.log(hashTable2.search(15)); // Output: false

// count occurence of distnict elements
const arr=["a","b","c","c","d","e","b","f","a"]

const result=arr.reduce((acc,cur) => cur in acc ? {...acc,[cur]:acc[cur] + 1}: {...acc,[cur]:1},{})
console.log(result);

// Intersection of two unsorted arrays using hash map
const arr1=[10,15,20,30,40,50,60]
const arr2=[14,10,46,20]

let m=arr1.length
let n=arr2.length

let a={}

for(let i=0;i<n;i++){
    a[arr2[i]]=1
}

for(let i=0;i<m;i++){
    if(a[arr1[i]]===1){
        console.log(arr1[i]);
    }
}

// Frequencies of array elements using hashmap
const arr3=[10,5,10,15,10,5]

arr3.sort((a,b) => a-b)

let n1=arr3.length

let x={}

for(let i=0;i<n1;i++){
    if(arr3[i] in x){
        x[arr3[i]]++
    }
    else{
        x[arr3[i]]=1
    }
}
console.log(x);

function CheckEqual(arr1, arr2) {
  if (arr1.length !== arr2.length) return false;

  const count = {};

  for (let val of arr1) {
    count[val] = (count[val] || 0) + 1;
  }

  for (let val of arr2) {
    if (!count[val]) {
      return false;
    }
    count[val]--;
  }

  return true;
}

console.log(CheckEqual([1, 2, 5, 4, 0], [2, 4, 5, 0, 1])); // true
console.log(CheckEqual([1, 2, 5], [2, 4, 15]));           // false
console.log(CheckEqual([1, 2, 2], [2, 1, 2]));            // true
console.log(CheckEqual([1, 2, 2], [2, 1, 1]));            // false

// count of non repeated elements
function countNonRepeated(arr) {
  const count = {};
  let nonRepeatedCount = 0;
  let filter=[]

  for (let val of arr) {
    count[val] = (count[val] || 0) + 1;
  }

  for (let val in count) {
    if (count[val] === 1) {
      filter.push(val)
      nonRepeatedCount++;
    }
  }

  return filter;
}

console.log(countNonRepeated([1,1,2,2,3,3,4,5,6,7]));

class Solution {
    // Function to check if there is a pair with the given sum in the array.
    sumExists(arr, N, sum) {
        // using map to store the elements.
        let s = new Map();
  
        // inserting all elements in the map.
        for (let i = 0; i < N; i++) s.set(arr[i], 1);

        // iterating over the array.
        for (let i = 0; i < N; i++) {
            // taking care of cases like 4-2=2 as two 2's cannot exist in
            // distinct array so we continue iteration over next element.
            if (sum - arr[i] == arr[i])
                continue;
            else {
                // if (sum-arr[i]) exists in map, we return 1.
                if (s.has(sum - arr[i])) {
                  //  console.log(`(${arr[i]}, ${sum - arr[i]})`);
                   return 1
                }

            }
           
        }
        // if no such pair is present, we return 0.
        return 0;
    }
}

console.log(new Solution().sumExists([1, 2, 3, 4, 5], 5, 9));

