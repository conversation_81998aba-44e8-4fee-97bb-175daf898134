"use strict";

class Node {
  constructor(data, next = null) {
    this.data = data;
    this.next = next;
  }
}

function printList(head) {
  let temp = head;
  while (temp != null) {
    console.log(temp.data);
    temp = temp.next;
  }
}

let head = new Node(1);
head.next = new Node(2);
head.next.next = new Node(3);
head.next.next.next = new Node(4);
head.next.next.next.next = new Node(5);

var removeNthFromEnd = function (head, n) {
  let dummy = new Node(0);
  dummy.next = head;
  let fast = dummy;
  let slow = dummy;

  // Move fast ahead by n steps
  for (let i = 0; i < n; i++) {
    fast = fast.next;
  }

  // Move both until fast reaches the last node
  while (fast.next != null) {
    fast = fast.next;
    slow = slow.next;
  }

  // Remove the nth node from the end
  slow.next = slow.next.next;

  return dummy.next;
};

// Remove 2nd node from end and print the updated list
let newHead = removeNthFromEnd(head, 2);
printList(newHead);

// Search insert
var searchInsert = function (nums, target) {
  for (let i = 0; i < nums.length; i++) {
    if (nums[i] >= target) {
      return i;
    }
  }
  // If target is greater than all elements, insert at the end
  return nums.length;
};

console.log(searchInsert([1, 3, 5, 6], 5)); 
console.log(searchInsert([1, 3, 5, 6], 2)); 
console.log(searchInsert([1, 3, 5, 6], 7));
console.log(searchInsert([1, 3, 5, 6], 0)); 
