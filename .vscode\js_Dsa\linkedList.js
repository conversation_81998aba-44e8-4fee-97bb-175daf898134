' use strict'

// Traversal of linked list
class Node {
    constructor(data, next = null) {
        this.data = data;
        this.next = next;
    }
}

function printList(head){
    let temp = head;
    while(temp != null){
        console.log(temp.data);
        temp = temp.next;
    }
}

head=new Node(10)
head.next=new Node(20)
head.next.next=new Node(30)
head.next.next.next=new Node(40)
printList(head)

// recursive Traversal of linked list
function printListrec(head){
    if(head == null){
        return;
    }
    console.log(head.data);
    printListrec(head.next);
}

printListrec(head)

// Insert at the end of linked list
class Node2 {
    constructor(data, next = null) {
        this.data = data;
        this.next = next;
    }
}

function insertAtTheEnd(head2,x){
   let temp =new Node2(x)
   if(head2===null)
    return temp
   let current =head2
   while(current.next!=null){
    current=current.next
   }
   current.next=temp
   return head2
}

let head2=null
head2=insertAtTheEnd(head2,10)
head2=insertAtTheEnd(head2,20)
head2=insertAtTheEnd(head2,30)

function printList2(head2) {
    let current = head2;
    let result = '';
    while (current !== null) {
        result += current.data + ' -> ';
        current = current.next;
    }
    console.log(result + 'null');
}

printList2(head2); 

// Delete the first node of linked list
function deleteTheFirstNode(head){
if(head===null)
    return head
return head.next
}

head=deleteTheFirstNode(head)
printList(head)

// insert at given position in a given single linked list
class Node4 {
    constructor(data, next = null) {
        this.data = data;
        this.next = next;
    }
}

function insertPos(head4,pos,data){
let temp=new Node4(data)
if(pos===1){
    temp.next=head4
    return temp
}
let current=head4
for(let i=1;i<=pos-2 && current!=null;i++){
    current=current.next
}
if(current===null){
    return head4
}
temp.next=current.next
current.next=temp
return head4
}

let head4=null
head4=insertPos(head4,1,10)
head4=insertPos(head4,2,20)
head4=insertPos(head4,3,30)
head4=insertPos(head4,2,15)
printList(head4)

// search in linked list
function search(head5,x){
    let pos=1
    let current=head5
    while(current!=null){
        if(current.data===x){
            return pos
        }
        pos++
        current=current.next
    }
    return -1
}

let head5=null
head5=insertPos(head5,1,10)
head5=insertPos(head5,2,20)
head5=insertPos(head5,3,30)
console.log(search(head5,20))

// search in a linked list recursively
function searchRec(head6,x){
    if(head6===null){
        return -1
    }
    if(head6.data===x){
        return 1
    }
    let pos=searchRec(head6.next,x)
    if(pos===-1){
        return -1
    }
    return 1+pos
}

let head6=null
head6=insertPos(head6,1,10)
head6=insertPos(head6,2,20)
head6=insertPos(head6,3,30)
console.log(searchRec(head6,20))

// sorted insert in a linked list
class Node7 {
    constructor(data, next = null) {
        this.data = data;
        this.next = next;
    }
}

function sortedInsert(head7,x){
    let temp=new Node7(x)
    if(head7===null){
        return temp
    }
    if(x<head7.data){
        temp.next=head7
        return temp
    }
    let current=head7
    while(current.next!=null && current.next.data<x){
        current=current.next
    }
    temp.next=current.next
    current.next=temp
    return head7
}

let head7=null
head7=sortedInsert(head7,10)
head7=sortedInsert(head7,20)
head7=sortedInsert(head7,30)
head7=sortedInsert(head7,15)
printList(head7)

// Middle of linked list
function findMid(head7) {
    if (head7 === null) return ;

    let count = 0;
    let temp = head7;

    // Count the number of nodes
    while (temp !== null) {
        count++;
        temp = temp.next;
    }

    // Traverse to the middle node
    let midIndex = Math.floor(count / 2);
    let curr = head7;

    for (let i = 0; i < midIndex; i++) {
        curr = curr.next;
    }

    return curr.data;
}

console.log(findMid(head7));

// Middle of a linked list approach 2
function findMid2(head7){
if (head7 === null) return ;

let slow=head7
let fast=head7

while(fast!==null && fast.next!==null){
slow=slow.next
fast=fast.next.next
}
return slow.data
}

console.log(findMid2(head7));

// print nth node from the end method 1
function PrintNthFromEnd(head7,n){
    let len=0
    for(let curr=head7;curr!==null;curr=curr.next){
        len++
    }
    if(len<n) return
    let curr=head7
    for(let i=1;i<len-n+1;i++)
        curr=curr.next
    return curr.data
}

console.log(PrintNthFromEnd(head7,3));

// print nth node from the end method 2
function PrintNthFromEnd2(head7,n){
   if(head7===null) return
   let first=head7
   for(let i=0;i<n;i++){
    if(first===null) return
    first=first.next
   }
   let second=head7
    while(first!==null){
    first=first.next
    second=second.next
}
return second.data
}

console.log(PrintNthFromEnd2(head7,2));

// reverse a linked list using iterative way using naive approach
function reverseList(head7){
   let arr=[]
   for(let curr=head7;curr!==null;curr=curr.next){
     arr.push(curr.data)
   }
   for(let curr=head7;curr!==null;curr=curr.next){
      curr.data=arr.pop()
   }
  return head7
}

reverseList(head7)
printList(head7)

// reverse a linked list using iterative way using effective approach
function reverseList2(head7){
    let prev=null
    let curr=head7
    while(curr!==null){
        next=curr.next
        curr.next=prev
        curr=prev
        curr=next
    }
    return prev
}

head7=reverseList2(head7)
printList(head7)

// recursive reverse of a linled list
function reverseList3(curr,prev=null){
    if(head7===null|| head7.next===null) 
    return head7 
    let next=curr.next
    curr.next=prev
    reverseList3(next,curr)
}

console.log(reverseList3(head7,));