'use strict';

// Palindrome
function isPalindrome(num) {
    // Negative numbers are not palindromes
    if (num < 0) return false;

    let temp = num;
    let reverse = 0;

    while (temp !== 0) {
        let digit = temp % 10;
        reverse = reverse * 10 + digit;
        temp = Math.floor(temp / 10);
    }

    return num === reverse;
}

// Test cases
console.log(isPalindrome(-12321)); // false (negative)
console.log(isPalindrome(-12323)); // false (negative)
console.log(isPalindrome(12321));  // true (positive palindrome)
console.log(isPalindrome(12323));  // false (not palindrome)
console.log(isPalindrome(0));      // true (single digit)
console.log(isPalindrome(7));      // true (single digit)



