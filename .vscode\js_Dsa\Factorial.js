'use strict';

function factorial(n){
    let fac = 1;
    for( let i=n;i>=2;i--){
      fac*=i;
    }
    return fac;
    
}

function trinfac(n){
    // finding trailing zeros in the factorial
    let fac = 1;
    for( let i=n;i>=2;i--){
      fac*=i;
    }

    let count=0;
    while(fac%10===0){
        count++;
        fac/=10;
    }
    return count;
}

console.log(factorial(5));
console.log(trinfac(5));
console.log(factorial(10));
console.log(trinfac(10));
