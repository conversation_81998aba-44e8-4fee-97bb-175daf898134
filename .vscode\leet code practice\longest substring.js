"use strict";

// find the length of the longest without duplicate characters.
var lengthOfLongestSubstring = function (s) {
  let max = 0;
  let start = 0;
  let end = 0;
  let set = new Set();
  while (end < s.length) {
    if (!set.has(s[end])) {
      set.add(s[end]);
      end++;
      max = Math.max(max, end - start);
    } else {
      set.delete(s[start]);
      start++;
    }
  }
  return max;
};

console.log(lengthOfLongestSubstring("abcabcbb"));

// longest palindromic substring
var longestPalindrome = function (s) {
  let longest = "";

  for (let i = 0; i < s.length; i++) {
    let len1 = expandFromMiddle(s, i, i); // for odd length
    let len2 = expandFromMiddle(s, i, i + 1); // for even length

    if (len1.length > longest.length) {
      longest = len1;
    }

    if (len2.length > longest.length) {
      longest = len2;
    }
  }
  return longest;
};

var expandFromMiddle = function (s, left, right) {
  let i = 0;
  while (s[left - i] && s[left - i] === s[right + i]) {
    i++;
  }
  i--;
  return s.slice(left - i, right + i + 1);
};

console.log(longestPalindrome("babad"));

// reverse an integer
function reverseInteger(x){
  let sign = Math.sign(x);
  let reverse = 0;
  x = Math.abs(x);
  while (x > 0) {
    reverse = reverse * 10 + (x % 10);
    x = Math.floor(x / 10);
  }
  return sign * reverse;
}

console.log(reverseInteger(-123));
console.log(reverseInteger(123));
console.log(reverseInteger(120));

// the longest common prefix
var longestCommonPrefix = function(strs) {
    if (strs.length === 0) return "";
    if (strs.length === 1) return strs[0];

    let prefix = "";
    let firstString = strs[0];

    // Check each character position
    for (let i = 0; i < firstString.length; i++) {
        let currentChar = firstString[i];

        // Check if this character exists at the same position in all other strings
        for (let j = 1; j < strs.length; j++) {
            if (i >= strs[j].length || strs[j][i] !== currentChar) {
                return prefix;
            }
        }

        // If we reach here, all strings have the same character at position i
        prefix += currentChar;
    }

    return prefix;
};

// Test cases
console.log(longestCommonPrefix(["flower", "flow", "flight"])); // "fl"
console.log(longestCommonPrefix(["dog", "racecar", "car"])); // ""
console.log(longestCommonPrefix(["interspecies", "interstellar", "interstate"])); // "inters"
console.log(longestCommonPrefix([""])); // ""
console.log(longestCommonPrefix(["a"])); // "a"