"use strict";

// find the length of the longest without duplicate characters.
var lengthOfLongestSubstring = function (s) {
  let max = 0;
  let start = 0;
  let end = 0;
  let set = new Set();
  while (end < s.length) {
    if (!set.has(s[end])) {
      set.add(s[end]);
      end++;
      max = Math.max(max, end - start);
    } else {
      set.delete(s[start]);
      start++;
    }
  }
  return max;
};

console.log(lengthOfLongestSubstring("abcabcbb"));

// longest palindromic substring
var longestPalindrome = function (s) {
  let longest = "";

  for (let i = 0; i < s.length; i++) {
    let len1 = expandFromMiddle(s, i, i); // for odd length
    let len2 = expandFromMiddle(s, i, i + 1); // for even length

    if (len1.length > longest.length) {
      longest = len1;
    }

    if (len2.length > longest.length) {
      longest = len2;
    }
  }
  return longest;
};

var expandFromMiddle = function (s, left, right) {
  let i = 0;
  while (s[left - i] && s[left - i] === s[right + i]) {
    i++;
  }
  i--;
  return s.slice(left - i, right + i + 1);
};

console.log(longestPalindrome("babad"));
