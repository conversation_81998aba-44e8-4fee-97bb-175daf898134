" use strict";

class myHash {
  constructor(b) {
    this.bucket = b;
    this.table = [];
    for (let i = 0; i < b; i++) {
      this.table[i] = [];
    }
  }
  insert(x) {
    let i = x % this.bucket;
    this.table[i].push(x);
  }
  search(x) {
    let i = x % this.bucket;
    for (let j = 0; j < this.table[i].length; j++) {
      if (this.table[i][j] === x) {
        return true;
      }
    }
    return false;
  }
}

// Two sum
var twoSum = function (nums, target) {
  let map = new Map(); // Store value -> index mapping

  for (let i = 0; i < nums.length; i++) {
    let complement = target - nums[i];

    // Check if complement exists in map
    if (map.has(complement)) {
      return [map.get(complement), i];
    }

    // Store current number and its index
    map.set(nums[i], i);
  }

  return []; // No solution found
};

// Test cases
console.log("twoSum([2, 7, 11, 15], 9):", twoSum([2, 7, 11, 15], 9));
console.log("twoSum([3, 2, 4], 6):", twoSum([3, 2, 4], 6));
console.log("twoSum([3, 3], 6):", twoSum([3, 3], 6));

// longest consecutive Sequence
var longestConsecutive = function (nums) {
  if (nums.length === 0) return 0;

  let set = new Set(nums);
  let maxLength = 0;

  for (let num of set) {
    // Only start counting from the beginning of a sequence
    // (when num-1 is not in the set)
    if (!set.has(num - 1)) {
      let currentNum = num;
      let currentLength = 1;

      // Count consecutive numbers starting from this number
      while (set.has(currentNum + 1)) {
        currentNum++;
        currentLength++;
      }

      // Update maximum length found so far
      maxLength = Math.max(maxLength, currentLength);
    }
  }

  return maxLength;
};

// Test cases
console.log(
  "longestConsecutive([100, 4, 200, 1, 3, 2]):",
  longestConsecutive([100, 4, 200, 1, 3, 2])
);
console.log(
  "longestConsecutive([0, 3, 7, 2, 5, 8, 4, 6, 0, 1]):",
  longestConsecutive([0, 3, 7, 2, 5, 8, 4, 6, 0, 1])
);
console.log(
  "longestConsecutive([1, 0, 1, 2]):",
  longestConsecutive([1, 0, 1, 2])
);
console.log("longestConsecutive([]):", longestConsecutive([]));
console.log(
  "longestConsecutive([9,1,4,7,3,-1,0,5,8,-1,6]):",
  longestConsecutive([9, 1, 4, 7, 3, -1, 0, 5, 8, -1, 6])
);

// Missing number
var missingNumber = function (nums) {
  if (nums.length === 0) return;

  let set = new Set(nums);

  for (let num of set) {
    if (!set.has(num - 1)) {
      let curNum = num;
    }

    while (set.has(curNum + 1)) {
      let cur = curNum++;
    }
   
  }
   return cur + 1;
};

console.log(missingNumber([3,0,1]));
console.log(missingNumber([0,1]));
console.log(missingNumber([9,6,4,2,3,5,7,0,1]));
