' use strict'

class myHash{
constructor(b){
    this.bucket=b
    this.table=[]
    for(let i=0;i<b;i++){
        this.table[i]=[]
    }
}
insert(x){
        let i=x%this.bucket
        this.table[i].push(x)
    }
    search(x){
        let i=x%this.bucket
        for(let j=0;j<this.table[i].length;j++){
            if(this.table[i][j]===x){
                return true
            }
        }
        return false
    }
}

// Two sum
var twoSum = function(nums, target) {
    let hash = new myHash(nums.length)
    for (let i = 0; i < nums.length; i++) {
        hash.insert(nums[i])
    }
    for (let i = 0; i < nums.length; i++) {
        let diff = target - nums[i]
        if (hash.search(diff)) {
            return [i, nums.indexOf(diff)]
        }
    }
    return []
};

console.log(twoSum([2, 7, 11, 15], 9));
console.log(twoSum([3, 2, 4], 6));
console.log(twoSum([3,3]));