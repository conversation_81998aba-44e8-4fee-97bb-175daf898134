" use strict";

class myHash {
  constructor(b) {
    this.bucket = b;
    this.table = [];
    for (let i = 0; i < b; i++) {
      this.table[i] = [];
    }
  }
  insert(x) {
    let i = x % this.bucket;
    this.table[i].push(x);
  }
  search(x) {
    let i = x % this.bucket;
    for (let j = 0; j < this.table[i].length; j++) {
      if (this.table[i][j] === x) {
        return true;
      }
    }
    return false;
  }
}

// Two sum
var twoSum = function (nums, target) {
  let map = new Map(); // Store value -> index mapping

  for (let i = 0; i < nums.length; i++) {
    let complement = target - nums[i];

    // Check if complement exists in map
    if (map.has(complement)) {
      return [map.get(complement), i];
    }

    // Store current number and its index
    map.set(nums[i], i);
  }

  return []; // No solution found
};

// Test cases
console.log("twoSum([2, 7, 11, 15], 9):", twoSum([2, 7, 11, 15], 9));
console.log("twoSum([3, 2, 4], 6):", twoSum([3, 2, 4], 6));
console.log("twoSum([3, 3], 6):", twoSum([3, 3], 6));

// longest consecutive
var longestConsecutive = function (nums) {
  let set = new Set(nums);
  let longseq = [];

  for (let i = 0; i < nums.length; i++) {
    seq1 = nums[i] + 1;
    seq2 = nums[i] - 1;

    if (set.has(seq1) || set.has(seq2)) {
      longseq.push[nums[i]];
    }
  }
  let set2 = new Set(longseq);
  return set2.length;
};

console.log(longestConsecutive([100, 4, 200, 1, 3, 2]));
console.log(longestConsecutive([0, 3, 7, 2, 5, 8, 4, 6, 0, 1]));
console.log(longestConsecutive([1, 0, 1, 2]));
